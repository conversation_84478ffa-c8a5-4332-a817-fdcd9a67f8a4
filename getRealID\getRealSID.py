# coding = utf-8
"""
    @project: gkGps
    @Author：Waite0603
    @file： getRealSID.py
    @date：2023/11/16 23:05
    
    TODO: 从给的数据中获取的学校名称匹配数据源的网站 ID, 返回匹配成功以及失败的学校名称, 学校 ID
"""
import pandas as pd
import json

file_name = "内蒙古"
data = pd.read_excel(f"getRealID/{file_name}.xlsx", dtype=str, sheet_name=[0])[0]

print(data)

file_name += "本科"

# data/school_dic_name.json
with open("data/school_dic_name_not.json", 'r', encoding='utf-8') as f:
  zsgk_data = json.load(f)

school_special = {
  "复旦大学医学院": 3427,
  "山东大学威海分校": 1219,
  "国防科技大学": 939,
  "华北电力大学北京": 831,
  "华北电力大学保定": 591,
  "空军军医大学": 937,
  "陆军军医大学": 1227,
  "广州城市理工学院原华南理工大学广州学院": 2059,
  "上海体育学院": 324,
  "信阳师范学院": 876,
  "伊犁师范学院": 1036,
  "大连民族学院": 208,
  "武警工程学院": 1213,
  "民办黑龙江东方学院": 456,
  "上海应用技术学院": 315,
  "陕西理工学院": 371,
  "广东工业大学华立学院": 2056,
  "广东技术师范学院天河学院": 2058
}


def find_sid(school_name):
  # 切割
  try:
    school_name = school_name.replace(' ', '')

    school_name = school_name.replace('(', '').replace(')', '')
    school_name = school_name.replace('（', '').replace('）', '')

    if school_name in school_special:
      return school_special[school_name]
    if school_name in zsgk_data:
      return zsgk_data[school_name]
    return None

  except Exception as e:
    return school_name


# data = data[['院校专业组代码', '院校专业组名称']]


data = pd.DataFrame(data)
print(data)
data.columns = ['院校代号', '学校名称']

# 遍历 data, 使用 find_sid 匹配 sid
data['sid'] = data['学校名称'].apply(find_sid)

data.columns = ['old_sid', 'school_name', 'sid']

# 保存匹配失败的数据
data[data['sid'].isna()].to_excel(f"{file_name}匹配失败的数据.xlsx", index=False)

print(len(data[data['sid'].notna()]))

# 去重
data = data.drop_duplicates(subset=['sid'], keep='first')

print(len(data[data['sid'].notna()]))
# 保存匹配成功的数据
data[data['sid'].notna()].to_excel(f"{file_name}匹配成功的数据.xlsx", index=False)
