# -*- coding: utf-8 -*-
"""
@author: Waite
@time: 2023-04-30 23:10c
"""

import logging
import time
import random
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import pandas as pd
from tqdm import tqdm

from tools.response import get_recruit_plan_new

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('recruit_plan_0.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)


@dataclass
class Config:
    """配置类：存储所有配置参数"""
    PROVINCE_ID: int = 41  # 省份ID
    YEAR: int = 2025  # 年份
    TYPE_ID: int = 0  # 类型ID
    LOCAL_BATCH_ID: int = 44  # 批次ID（7-本科一批，8-本科二批，10-专科批）
    FILE_NAME: str = "getRealID/finish/河南专科匹配成功的数据.xlsx"
    MAX_RETRIES: int = 3  # 单个学校最大重试次数
    RETRY_ATTEMPTS: int = 2  # 失败列表重试次数
    SLEEP_TIME_403: Tuple[float, float] = (10.0, 15.0)  # 403错误等待时间范围
    SLEEP_TIME_OTHER: Tuple[float, float] = (2.0, 5.0)  # 其他错误等待时间范围
    SLEEP_TIME_RETRY: Tuple[float, float] = (5.0, 10.0)  # 重试间隔时间范围

    def __post_init__(self):
        """初始化代理设置"""
        self.PROXY = {
            'http': 'http://127.0.0.1:3060',
            'https': 'http://127.0.0.1:3060'
        }


class DataCollector:
    """数据采集器：处理学校数据的获取和处理"""

    def __init__(self, config: Config):
        """初始化数据采集器"""
        self.config = config
        self.logger = logging.getLogger(__name__)

    def load_school_list(self) -> List[Dict[int, str]]:
        """从Excel文件加载学校列表"""
        try:
            school_data = pd.read_excel(self.config.FILE_NAME)[['sid', 'school_name']]
            return [{int(i): j} for i, j in zip(school_data['sid'], school_data['school_name'])]
        except Exception as e:
            self.logger.error(f"加载学校列表失败: {str(e)}")
            raise

    def fetch_school_data(self, school_id: int, school_name: str) -> Tuple[List, bool]:
        """获取单个学校的数据（带重试机制）"""
        for attempt in range(self.config.MAX_RETRIES):
            try:
                self.logger.debug(f"正在获取数据: {school_name} (第{attempt + 1}次尝试)")
                response = get_recruit_plan_new(
                    self.config.PROVINCE_ID,
                    self.config.YEAR,
                    school_id,
                    school_name
                )

                if response['code'] == 200:
                    return response['data'], True

                sleep_time = (random.uniform(*self.config.SLEEP_TIME_403)
                              if response['code'] == 403
                              else random.uniform(*self.config.SLEEP_TIME_OTHER))

                self.logger.warning(f"获取数据失败 {response['code']} - {school_name}, 等待{sleep_time:.2f}秒")
                time.sleep(sleep_time)

            except Exception as e:
                self.logger.error(f"获取学校数据出错 {school_name}: {str(e)}")
                time.sleep(random.uniform(*self.config.SLEEP_TIME_OTHER))

        return [], False

    def process_schools(self, school_list: List[Dict[int, str]]) -> Tuple[List, List]:
        """处理学校列表，返回成功数据和失败学校"""
        data = []
        failed_schools = []

        for school_info in tqdm(school_list, desc="处理学校进度"):
            school_id = list(school_info.keys())[0]
            school_name = school_info[school_id]

            school_data, success = self.fetch_school_data(school_id, school_name)

            if success:
                data.extend(school_data)
            else:
                failed_schools.append(school_info)
                self.logger.warning(f"获取数据失败: {school_name} (ID: {school_id})")

        return data, failed_schools

    def save_data(self, data: List[Dict]) -> None:
        """保存处理后据到Excel文件"""
        timestamp = time.strftime('%Y%m%d%H%M%S')
        filename = (f"{self.config.PROVINCE_ID}_{self.config.TYPE_ID}_"
                    f"{self.config.YEAR}_{self.config.LOCAL_BATCH_ID}_"
                    f"get_recruit_plan.xlsx")

        try:
            pd.DataFrame(data).to_excel(filename, index=False)
            self.logger.info(f"数据已成功保存至: {filename}")
        except Exception as e:
            self.logger.error(f"保存数据失败: {str(e)}")
            raise


def main():
    """主执行函数"""
    config = Config()
    collector = DataCollector(config)

    try:
        # 加载学校列表
        school_list = collector.load_school_list()
        all_data = []
        current_schools = school_list

        # 处理学校数据（带重试机制）
        for retry_count in range(config.RETRY_ATTEMPTS):
            if not current_schools:
                break

            logging.info(f"正在处理 {len(current_schools)} 所学校 (第{retry_count + 1}次尝试)")
            data, failed_schools = collector.process_schools(current_schools)
            all_data.extend(data)

            if not failed_schools:
                break

            current_schools = failed_schools
            time.sleep(random.uniform(*config.SLEEP_TIME_RETRY))

        # 记录最终结果
        if failed_schools:
            logging.warning(f"经过所有尝试后，仍有 {len(failed_schools)} 所学校获取失败")
            for school in failed_schools:
                school_id = list(school.keys())[0]
                logging.warning(f"获取失败的学校: {school[school_id]} (ID: {school_id})")

        # 保存结果
        collector.save_data(all_data)

    except Exception as e:
        logging.error(f"执行过程中发生错误: {str(e)}")
        raise


if __name__ == "__main__":
    main()
