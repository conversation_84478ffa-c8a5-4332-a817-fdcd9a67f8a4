# coding = utf-8
"""
    @project: gkGpsShare
    @Author：Waite0603
    @file： professional_group.py
    @date：2025/3/8 上午10:42
    
    TODO:
"""
import pandas as pd
import numpy as np

fileName = 'processed_results.xlsx'
school_real_list_path = './getRealID/finish/河南专科匹配成功的数据.xlsx'    
data_school = pd.read_excel(school_real_list_path).to_dict('records')
# print(data_school)
data_school_dict = {row['school_name']: row['old_sid'] for row in data_school}
# print(data_school_dict)
data = pd.read_excel(fileName)
data['sg_name'] = data['sg_name'].apply(lambda x: str(x).replace('（', '').replace('）', ''))  # 去除括号内容
# data['school_id'] = data['school_name'].apply(lambda x: data_school_dict.get(x, x))  # 替换为真实的 school_id

# 预处理：将所有的 np.nan 替换为 '-'
data = data.fillna('-')

def format_number(num_str):
    """格式化数字字符串，去除不必要的 .0"""
    if num_str == '-':
        return num_str
    try:
        num = float(num_str)
        if num.is_integer():
            return str(int(num))
        return str(num)
    except (ValueError, TypeError):
        return str(num_str)

DATA = []

# 获得所有院校名单
school_list = set(data['school_id'].tolist())

print(school_list) 
for i in school_list:
    one_school = data[data['school_id'] == i]
    print(i)
    # 按专业组 组合 sg_name
    sg_name_list = list(set(one_school['sg_name'].tolist()))
    for j in sg_name_list:
        one_dict = {}
        one_sg_name = one_school[one_school['sg_name'] == j].to_dict(orient='records')
        # 排序 把 min 为 '-'的放到最后
        one_sg_name = sorted(one_sg_name, key=lambda x: x['2024_min'] == '-', reverse=False)
        # 根据 min 降序排序
        one_sg_name_not_ = [i for i in one_sg_name if i['2024_min'] != '-']
        one_sg_name_have_ = [i for i in one_sg_name if i['2024_min'] == '-']
        one_sg_name = sorted(one_sg_name_not_, key=lambda x: int(x['2024_min']), reverse=True) + one_sg_name_have_

        one_dict['school_id'] = i
        one_dict['school_name'] = one_sg_name[0]['school_name']
        one_dict['sg_name'] = str(j) + '/ \n\t' + one_sg_name[0]['sg_info']
        one_dict['null'] = ""
        zhuanye_list = [i['spname_copy'] for i in one_sg_name]
        one_dict['spname_copy'] = '\n\t'.join(zhuanye_list)

        num_list = [format_number(i['num']) for i in one_sg_name]
        tuition_list = [format_number(i['tuition']) for i in one_sg_name]
        num_list_2024 = [format_number(i['2024_num']) for i in one_sg_name]
        num_list_2023 = [format_number(i['2023_num']) for i in one_sg_name]
        num_list_2022 = [format_number(i['2022_num']) for i in one_sg_name]
        
        num_mix_tuition = []
        for i in range(len(num_list)):
            num_mix_tuition.append(f"{num_list[i]}/{tuition_list[i]}/{num_list_2024[i]}/{num_list_2023[i]}/{num_list_2022[i]}")
        
        one_dict['num'] = '\n\t'.join(num_mix_tuition)

        # 2024年数据处理
        min_2024_list = [format_number(i['2024_min']) for i in one_sg_name]
        min_section_2024_list = [format_number(i['2024_min_section']) for i in one_sg_name]
        min_values = [int(float(i)) for i in min_2024_list if i != '-']

        if min_values:
            max_min = min(min_values)
        else:
            max_min = "-"
        min_mix_section = [f"{min_2024_list[i]}/{min_section_2024_list[i]}" for i in range(len(min_2024_list))]
        one_dict['min'] = '\n\t'.join(min_mix_section)
        if format_number(str(max_min)) != "-":
            one_dict['min_max'] = int(format_number(str(max_min)))
        else:
            one_dict['min_max'] = '-'

        # 2023年数据处理
        min_2023_list = [format_number(i['2023_min']) for i in one_sg_name]
        min_section_2023_list = [format_number(i['2023_min_section']) for i in one_sg_name]
        one_dict['min_2023'] = '\n\t'.join(
            [f"{min_2023_list[i]}/{min_section_2023_list[i]}" for i in range(len(min_2023_list))])

        # 2022年数据处理
        min_2022_list = [format_number(i['2022_min']) for i in one_sg_name]
        min_section_2022_list = [format_number(i['2022_min_section']) for i in one_sg_name]
        one_dict['min_2022'] = '\n\t'.join(
            [f"{min_2022_list[i]}/{min_section_2022_list[i]}" for i in range(len(min_2022_list))])

        DATA.append(one_dict)


# 根据min_max排序， '-' 放到最后， 其他按照从大到小排序
# 分成两部分 一部分是有最低分的，一部分是没有最低分的
DATA1 = [i for i in DATA if i['min_max'] != '-']
DATA1 = sorted(DATA1, key=lambda x: int(x['min_max']), reverse=True)
DATA2 = [i for i in DATA if i['min_max'] == '-']

DATA = DATA1 + DATA2

# 保存数据
df = pd.DataFrame(DATA)
df['school_id'] = df['school_name'].map(data_school_dict)
df['province'] = '河南省'
df['0'] = '1'
df['2'] = '2'
df['2025'] = '2024'
df.to_excel("河南省-18-2024-1-2.xlsx", index=False)